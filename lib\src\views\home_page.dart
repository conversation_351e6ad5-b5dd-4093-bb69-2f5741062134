import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:eddyowl_student_mobile/src/widgets/page_header.dart';
import 'package:eddyowl_student_mobile/src/widgets/subject_card.dart';
import 'package:eddyowl_student_mobile/src/data/subjects_data.dart';
import 'package:eddyowl_student_mobile/src/utils/colors_map.dart';

class HomePage extends StatelessWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          const PageHeader(
            title: "Home",
            subtitle: "Select a subject",
          ),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: ListView.builder(
                itemCount: subjects.length,
                itemBuilder: (context, index) {
                  final subject = subjects[index];
                  final subjectName = subject["subjectName"];

                  // Find the corresponding color map entry
                  final colorData = colorsMap.firstWhere(
                    (map) => map["subjectName"] == subjectName,
                    orElse: () => {
                      "imagePath": "assets/images/ic_default.png",
                      "primaryColor": 0xFF2196F3,
                    },
                  );

                  return GestureDetector(
                    onTap: () {
                      context.push('/assignments/$subjectName');
                    },
                    child: SubjectCard(
                      subjectName: subjectName,
                      imagePath: colorData["imagePath"],
                      primaryColor: Color(colorData["primaryColor"]),
                    ),
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }
}
