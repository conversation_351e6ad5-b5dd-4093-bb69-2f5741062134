import 'package:flutter/material.dart';

class AssignmentCard extends StatelessWidget {
  final String name;
  final String subject;
  final List<String> topics;
  final String status;
  final String date;
  final int totalScore;
  final int achievedScore;
  final Color? variantColor;

  const AssignmentCard({
    super.key,
    required this.name,
    required this.subject,
    required this.topics,
    required this.status,
    required this.date,
    required this.totalScore,
    required this.achievedScore,
    this.variantColor,
  });

  @override
  Widget build(BuildContext context) {
    Color statusColor;
    String statusText;
    Icon statusIcon;

    switch (status) {
      case "graded":
        statusColor = achievedScore < 50
            ? Colors.red
            : (achievedScore < 70 ? Colors.amber : Colors.green);
        statusText = "$achievedScore / $totalScore";
        statusIcon =
            const Icon(Icons.check_circle, size: 12, color: Colors.white);
        break;
      case "due":
        statusColor = Colors.amber;
        statusText = "Due";
        statusIcon = const Icon(Icons.error, size: 12, color: Colors.white);
        break;
      case "processing":
        statusColor = Colors.blueAccent;
        statusText = "Processing";
        statusIcon =
            const Icon(Icons.access_time, size: 12, color: Colors.white);
        break;
      case "failed":
        statusColor = Colors.red;
        statusText = "Failed";
        statusIcon = const Icon(Icons.close, size: 12, color: Colors.white);
        break;
      default:
        statusColor = Colors.grey;
        statusText = "Unknown";
        statusIcon = const Icon(Icons.help, size: 12, color: Colors.white);
    }

    return Card(
      color: variantColor ?? Colors.white,
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 20),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(name,
                      style: const TextStyle(
                          fontSize: 16, fontWeight: FontWeight.bold)),
                  Text(subject,
                      style: const TextStyle(fontSize: 12, color: Colors.blueGrey)),
                  const SizedBox(height: 5),
                  Container(
                    constraints: BoxConstraints(maxWidth: 150),
                    child: SingleChildScrollView(
                      scrollDirection: Axis.horizontal,
                      child: Text(topics.join(", "),
                          style: const TextStyle(
                              fontSize: 12, color: Colors.black87)),
                    ),
                  ),
                  const SizedBox(height: 15),
                  Container(
                    decoration: BoxDecoration(
                      color: statusColor,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 3),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        statusIcon,
                        const SizedBox(width: 4),
                        Text(statusText,
                            style: const TextStyle(
                                fontSize: 12, color: Colors.white)),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(date,
                    style: const TextStyle(fontSize: 12, color: Colors.amber)),
                const SizedBox(height: 55),
                Row(
                  children: const [
                    Icon(Icons.remove_red_eye, size: 14, color: Colors.black),
                    SizedBox(width: 4),
                    Text("View Assignment",
                        style: TextStyle(fontSize: 12, color: Colors.black)),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
