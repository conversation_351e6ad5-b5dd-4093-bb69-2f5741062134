import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'src/views/landing_page.dart';
import 'src/views/auth_page.dart';
import 'src/views/account_page.dart';
import 'src/views/assignments_page.dart';
import 'src/views/home_page.dart';
import 'src/views/main_screen.dart';
import 'src/views/notifications_page.dart';
import 'src/views/view_assignment_page.dart';
import 'src/services/auth_service.dart';

class MyApp extends StatelessWidget {
  MyApp({super.key});

  late final _router = GoRouter(
    initialLocation: '/',
    redirect: (BuildContext context, GoRouterState state) {
      final bool isLoggedIn = context.read<AuthService>().isAuthenticated;
      final bool isAuthRoute = state.matchedLocation == '/' ||
                              state.matchedLocation == '/login' ||
                              state.matchedLocation == '/signup';

      if (!isLoggedIn && !isAuthRoute) {
        return '/';
      }
      if (isLoggedIn && isAuthRoute) {
        return '/assignments';
      }
      return null;
    },
    routes: [
      GoRoute(
        path: '/',
        builder: (context, state) => const LandingPage(),
      ),
      GoRoute(
        path: '/login',
        builder: (context, state) => const AuthPage(isSignUp: false),
      ),
      GoRoute(
        path: '/signup',
        builder: (context, state) => const AuthPage(isSignUp: true),
      ),
      ShellRoute(
        builder: (context, state, child) {
          return MainScreen(child: child);
        },
        routes: [
          GoRoute(
            path: '/home',
            builder: (context, state) => const HomePage(),
          ),
          GoRoute(
            path: '/assignments',
            builder: (context, state) => const AssignmentsPage(),
          ),
          GoRoute(
            path: '/assignments/:subject',
            builder: (context, state) {
              final subject = state.pathParameters['subject'];
              return AssignmentsPage(subject: subject);
            },
          ),
          GoRoute(
            path: '/assignments/:subject/view-assignment/:assignmentName',
            builder: (context, state) {
              final subject = state.pathParameters['subject'];
              final assignmentName = state.pathParameters['assignmentName'];
              return ViewAssignmentPage(
                  subject: subject, assignmentName: assignmentName);
            },
          ),
          GoRoute(
            path: '/notifications',
            builder: (context, state) => const NotificationsPage(),
          ),
          GoRoute(
            path: '/account',
            builder: (context, state) => const AccountPage(),
          ),
        ],
      ),
    ],
  );

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => AuthService(),
      child: MaterialApp.router(
        routerConfig: _router,
        title: 'EddyOwl Student',
        theme: ThemeData(
          primarySwatch: Colors.blue,
          visualDensity: VisualDensity.adaptivePlatformDensity,
        ),
      ),
    );
  }
}
