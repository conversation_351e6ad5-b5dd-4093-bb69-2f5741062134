const List<Map<String, dynamic>> assignments = [
  {
    "name": "Unit Test 1",
    "subjectName": "Maths",
    "topics": ["Linear Equation", "Algebra", "Trigonometry", "Geometry"],
    "status": "graded",
    "date": "20 Sept",
    "totalScore": 100,
    "achievedScore": 95,
  },
  {
    "name": "Unit Test 2",
    "subjectName": "Science",
    "topics": ["Photosynthesis", "Evolution"],
    "status": "due",
    "date": "25 Sept",
    "totalScore": 0,
    "achievedScore": 0,
  },
  {
    "name": "Unit Test 2",
    "subjectName": "English",
    "topics": ["Grammar", "Vocabulary"],
    "status": "graded",
    "date": "20 Sept",
    "totalScore": 100,
    "achievedScore": 60,
  },
  {
    "name": "Mid Term Exam",
    "subjectName": "Social Science",
    "topics": ["World War I", "Industrial Revolution"],
    "status": "processing",
    "date": "30 Sept",
    "totalScore": 0,
    "achievedScore": 0,
  },
  {
    "name": "Final Exam",
    "subjectName": "Computer Science",
    "topics": ["Maps", "Climate"],
    "status": "failed",
    "date": "5 Oct",
    "totalScore": 0,
    "achievedScore": 30,
  },
];
