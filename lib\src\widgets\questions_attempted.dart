import 'package:flutter/material.dart';

class QuestionsAttempted extends StatefulWidget {
  final List<Map<String, dynamic>> studentResponses;
  final ScrollController scrollController;
  final Color primaryColor;
  final Color secondaryColor;

  const QuestionsAttempted({
    super.key,
    required this.studentResponses,
    required this.scrollController,
    this.primaryColor = const Color(0xFFE8F5E9), // default color
    this.secondaryColor = const Color(0xFFF5F5F5), // default lighter shade
  });

  @override
  QuestionsAttemptedState createState() => QuestionsAttemptedState();
}

class QuestionsAttemptedState extends State<QuestionsAttempted> {
  int? selectedQuestion;
  Map<String, bool> expandedStates = {};
  bool isAccordionExpanded = true;

  @override
  void initState() {
    super.initState();
    selectedQuestion = widget.studentResponses.isNotEmpty
        ? widget.studentResponses.first["questionNumber"]
        : null;
  }

  String _truncateText(String text, int maxWords) {
    List<String> words = text.split(' ');
    return words.length > maxWords
        ? '${words.take(maxWords).join(' ')}...'
        : text;
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(5),
        child: ExpansionTile(
          initiallyExpanded: false,
          maintainState: true,
          shape: const RoundedRectangleBorder(
            side: BorderSide(color: Colors.transparent),
          ),
          collapsedShape: const RoundedRectangleBorder(
            side: BorderSide(color: Colors.transparent),
          ),
          onExpansionChanged: (expanded) {
            setState(() {
              isAccordionExpanded = expanded;
            });
          },
          backgroundColor: widget.primaryColor,
          collapsedBackgroundColor: widget.primaryColor,
          leading: Icon(
            Icons.check,
            color: Colors.green,
          ),
          trailing: Icon(
            isAccordionExpanded
                ? Icons.keyboard_arrow_up
                : Icons.keyboard_arrow_down,
            color: Colors.black,
          ),
          title: const Text(
            "Questions Attempted",
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          children: [
            Container(
              decoration: BoxDecoration(
                color: widget.primaryColor,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    width: double.infinity,
                    color: widget.primaryColor,
                    padding:
                        const EdgeInsets.symmetric(vertical: 5, horizontal: 5),
                    child: SingleChildScrollView(
                      scrollDirection: Axis.horizontal,
                      child: Row(
                        children: widget.studentResponses.map((response) {
                          bool isSelected =
                              response["questionNumber"] == selectedQuestion;
                          return GestureDetector(
                            onTap: () {
                              setState(() {
                                selectedQuestion = response["questionNumber"];
                              });
                            },
                            child: Container(
                              margin: const EdgeInsets.symmetric(
                                  horizontal: 4, vertical: 8),
                              padding: const EdgeInsets.all(15),
                              decoration: BoxDecoration(
                                color: isSelected
                                    ? Colors.blue
                                    : Colors.lightBlue.shade100,
                                shape: BoxShape.circle,
                              ),
                              child: Text(
                                "Q${response["questionNumber"]}",
                                style: TextStyle(
                                  color:
                                      isSelected ? Colors.white : Colors.black,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          );
                        }).toList(),
                      ),
                    ),
                  ),
                  Container(
                    width: double.infinity,
                    decoration: BoxDecoration(
                      color: widget.secondaryColor,
                      borderRadius: const BorderRadius.only(
                        bottomLeft: Radius.circular(20),
                        bottomRight: Radius.circular(20),
                      ),
                    ),
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        if (selectedQuestion != null)
                          ...widget.studentResponses
                              .where((response) =>
                                  response["questionNumber"] ==
                                  selectedQuestion)
                              .map((response) => Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      _infoRow(
                                          "Question",
                                          "${response["achievedScore"]}/${response["questionScore"]}",
                                          response),
                                      const Divider(thickness: 1.2),
                                      _expandableText(response["question"],
                                          "question_${response["questionNumber"]}"),
                                      const Divider(thickness: 1.2),
                                      _sectionTitle("Response"),
                                      _expandableText(
                                          response["studentResponse"],
                                          "response_${response["questionNumber"]}"),
                                      const Divider(thickness: 1.2),
                                      _sectionTitle("Feedback"),
                                      _expandableText(response["feedback"],
                                          "feedback_${response["questionNumber"]}"),
                                    ],
                                  )),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _sectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Text(
        title,
        style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
      ),
    );
  }

  Widget _infoRow(String label, String value, Map<String, dynamic> response) {
    getScoreColor(Map<String, dynamic> response) {
      if (response["achievedScore"] == response["questionScore"]) {
        return Colors.green;
      } else if (response["achievedScore"] == 0) {
        return Colors.red;
      } else {
        return Colors.amber;
      }
    }

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 5),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: getScoreColor(response),
            ), // Fixed closing parenthesis here
          ),
        ],
      ),
    );
  }

  Widget _expandableText(String text, String key) {
    if (!expandedStates.containsKey(key)) {
      expandedStates[key] = false;
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          expandedStates[key]! ? text : _truncateText(text, 20),
          style: const TextStyle(fontSize: 14),
        ),
        if (text.split(' ').length > 20)
          InkWell(
            onTap: () {
              setState(() {
                expandedStates[key] = !expandedStates[key]!;
              });
            },
            child: Text(
              expandedStates[key]! ? "Show Less" : "Read More",
              style: const TextStyle(color: Colors.blue),
            ),
          ),
      ],
    );
  }
}
