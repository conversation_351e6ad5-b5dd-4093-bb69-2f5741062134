import 'package:flutter/material.dart';
import 'package:eddyowl_student_mobile/src/widgets/custom_search_bar.dart';
import 'package:eddyowl_student_mobile/src/widgets/assignment_card.dart';
import 'package:eddyowl_student_mobile/src/data/assignments_data.dart';
import 'package:eddyowl_student_mobile/src/widgets/page_header.dart';
import 'package:eddyowl_student_mobile/src/utils/colors_map.dart';
import 'package:go_router/go_router.dart';

class AssignmentsPage extends StatelessWidget {
  final String? subject;

  const AssignmentsPage({super.key, this.subject});

  /// Get colors from colorsMap based on subject name
  Map<String, dynamic>? getSubjectColors(String subjectName) {
    return colorsMap.firstWhere(
      (map) => map["subjectName"] == subjectName.trim(),
      orElse: () => {},
    );
  }

  @override
  Widget build(BuildContext context) {
    final TextEditingController searchController = TextEditingController();

    // If subject is provided, fetch its colors, else use defaults
    final subjectColors = subject != null ? getSubjectColors(subject!) : null;
    final buttonVariantColor = subjectColors?["primaryColor"] != null
        ? Color(subjectColors!["primaryColor"])
        : Color(0xFF03A9F4);

    return Scaffold(
      body: Column(
        children: [
          PageHeader(
            title: subject != null ? "Assignments for $subject" : "Assignments",
            subtitle: "Select an assignment to upload",
          ),
          CustomSearchBar(
            controller: searchController,
            onPressed: () {},
            secondaryColor:
                buttonVariantColor, // Use subject color if available
            subject: subject,
          ),
          const SizedBox(height: 5),
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 0),
              itemCount: assignments.length,
              itemBuilder: (context, index) {
                final assignment = assignments[index];
                final assignmentSubject = assignment["subjectName"] as String;

                // If a subject is selected, use its colors; otherwise, use per-assignment subject color
                final assignmentColors = subject != null
                    ? subjectColors
                    : getSubjectColors(assignmentSubject);

                final assignmentCardColor =
                    assignmentColors?["secondaryColor"] != null
                        ? Color(assignmentColors!["secondaryColor"])
                        : Color(0xFFE8F4FD);

                return GestureDetector(
                  onTap: () {
                    context.push(
                        '/assignments/${assignment["subjectName"]}/view-assignment/${assignment["name"]}');
                  },
                  child: AssignmentCard(
                    name: assignment["name"],
                    subject: assignment["subjectName"],
                    topics: List<String>.from(assignment["topics"]),
                    status: assignment["status"],
                    date: assignment["date"],
                    totalScore: assignment["totalScore"],
                    achievedScore: assignment["achievedScore"],
                    variantColor: assignmentCardColor,
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
