import 'package:eddyowl_student_mobile/src/data/student_responses_data.dart';
import 'package:flutter/material.dart';
import 'package:eddyowl_student_mobile/src/widgets/page_header.dart';
import 'package:eddyowl_student_mobile/src/widgets/questions_attempted.dart';

class ViewAssignmentPage extends StatefulWidget {
  final String? subject;
  final String? assignmentName;

  const ViewAssignmentPage({
    super.key,
    this.subject,
    this.assignmentName,
  });

  @override
  State<ViewAssignmentPage> createState() => _ViewAssignmentPageState();
}

class _ViewAssignmentPageState extends State<ViewAssignmentPage> {
  final ScrollController _scrollController = ScrollController();

  String _getGreetingText(int achievedScore) {
    return achievedScore < 50 ? "Average" : (achievedScore < 70 ? "Good!" : "Great!");
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        child: Column(
          children: [
            PageHeader(
              title: _getGreetingText(75),
              subtitle: "You scored higher than 75% of students in your class",
              achievedScore: 74,
              totalScore: 100,
            ),
            const SizedBox(height: 10),
            Align(
              alignment: Alignment.centerLeft,
              child: Padding(
                padding: const EdgeInsets.only(left: 20),
                child: const Text(
                  "Summary",
                  style: TextStyle(
                    fontSize: 28,
                    fontWeight: FontWeight.w300,
                  ),
                ),
              ),
            ),
            const SizedBox(height: 10),
            SingleChildScrollView(
              child: QuestionsAttempted(
                studentResponses: studentResponses,
                scrollController: _scrollController,
                primaryColor: Color(0xFFE8F5E9),
                secondaryColor: Color(0xFFF5F5F5),
              ),
            ),
            const SizedBox(height: 10),
          ],
        ),
      ),
    );
  }
}
