import 'package:flutter/material.dart';

class BottomNavBar extends StatelessWidget {
  final int currentIndex;
  final ValueChanged<int> onTap;

  const BottomNavBar({
    super.key,
    required this.currentIndex,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        border: Border(
          top: BorderSide(color: Colors.grey, width: 0.1),
        ),
      ),
      child: BottomNavigationBar(
        currentIndex: currentIndex,
        onTap: onTap,
        selectedItemColor: const Color(0xFF00B7EB),
        unselectedItemColor: Colors.grey,
        showSelectedLabels: true,
        showUnselectedLabels: true,
        type: BottomNavigationBarType.fixed,
        items: [
          _buildNavItem(Icons.home, "Home", 0),
          _buildNavItem(Icons.assignment, "Assignments", 1),
          _buildNavItem(Icons.notifications, "Notifications", 2),
          _buildNavItem(Icons.person, "Account", 3),
        ],
      ),
    );
  }

  BottomNavigationBarItem _buildNavItem(
      IconData icon, String label, int index) {
    return BottomNavigationBarItem(
      icon: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (index == currentIndex)
            Container(
              width: 30,
              height: 3,
              decoration: BoxDecoration(
                color: const Color(0xFF00B7EB),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          const SizedBox(height: 10),
          Icon(
            icon,
            size: 24,
            color:
                index == currentIndex ? const Color(0xFF00B7EB) : Colors.grey,
          ),
          const SizedBox(height: 5),
        ],
      ),
      label: label,
    );
  }
}
