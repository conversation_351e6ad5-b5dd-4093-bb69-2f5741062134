import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:eddyowl_student_mobile/src/widgets/bottom_nav_bar.dart';

class MainScreen extends StatelessWidget {
  final Widget child;

  const MainScreen({super.key, required this.child});

  @override
  Widget build(BuildContext context) {
    String location = GoRouterState.of(context).uri.toString();

    int getSelectedIndex() {
      if (location == '/home') return 0;
      if (location.startsWith('/assignments')) return 1;
      if (location == '/notifications') return 2;
      if (location == '/account') return 3;
      return 0;
    }

    int selectedIndex = getSelectedIndex();

    return Scaffold(
      body: child, // Show the active page
      bottomNavigationBar: BottomNavBar(
        currentIndex: selectedIndex,
        onTap: (index) {
          switch (index) {
            case 0:
              context.push('/home');
              break;
            case 1:
              context.push('/assignments');
              break;
            case 2:
              context.push('/notifications');
              break;
            case 3:
              context.push('/account');
              break;
          }
        },
      ),
    );
  }
}
