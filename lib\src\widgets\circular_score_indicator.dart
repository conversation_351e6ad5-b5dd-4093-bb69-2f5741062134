import 'package:flutter/material.dart';
import 'package:percent_indicator/percent_indicator.dart';

class CircularScoreIndicator extends StatelessWidget {
  final int achievedScore;
  final int totalScore;

  const CircularScoreIndicator({
    super.key,
    required this.achievedScore,
    this.totalScore = 100,
  });

  Color _getScoreColor(int score) {
    return score < 50 ? Colors.red : (score < 70 ? Colors.amber : Colors.green);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        shape: BoxShape.circle,
        color: Colors.white,
      ),
      child: CircularPercentIndicator(
        radius: 85.0,
        lineWidth: 6.0,
        percent: achievedScore / totalScore,
        center: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              '$achievedScore',
              style: TextStyle(
                fontSize: 60,
                fontWeight: FontWeight.w400,
                color: _getScoreColor(achievedScore),
              ),
            ),
            Transform.translate(
              offset: const Offset(0, -10),
              child: Text(
                'of $totalScore',
                style: const TextStyle(
                  fontSize: 30,
                  fontWeight: FontWeight.w400,
                  color: Colors.green,
                ),
              ),
            ),
          ],
        ),
        backgroundColor: Colors.amber,
        progressColor: _getScoreColor(achievedScore),
        circularStrokeCap: CircularStrokeCap.round,
        animation: true,
        animationDuration: 1000,
      ),
    );
  }
}

