import 'package:flutter/material.dart';

class SubjectCard extends StatelessWidget {
  final String subjectName;
  final String imagePath;
  final Color primaryColor;

  const SubjectCard({
    super.key,
    required this.subjectName,
    required this.imagePath,
    required this.primaryColor,
  });

  /// Function to get initials from subject name
  String getSubjectInitials(String name) {
    List<String> words = name.split(" ");
    return words.length > 1 ? words[0][0] + words[1][0] : words[0][0];
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 5, vertical: 8),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        color: Colors.white,
        border: Border.all(color: primaryColor),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha((0.1 * 255).toInt()),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(15),
        child: Row(
          children: [
            /// **Curved Gradient Section (20% of Card Width)**
            ClipPath(
              clipper: RightCurveClipper(),
              child: ShaderMask(
                shaderCallback: (Rect bounds) {
                  return RadialGradient(
                    center: Alignment
                        .center, // Changed to center for all-around effect
                    radius:
                        0.9, // Reduced radius to spread the effect more evenly
                    colors: [
                      Colors.transparent,
                      Colors.black.withAlpha((0.3 * 255).toInt()),
                    ],
                    stops: const [0.1, 1.0], // Adjusted stops for better spread
                  ).createShader(bounds);
                },
                blendMode: BlendMode.darken,
                child: Container(
                  width: MediaQuery.of(context).size.width * 0.20,
                  height: 80,
                  decoration: BoxDecoration(
                    color: primaryColor,
                  ),
                  padding: const EdgeInsets.only(right: 12),
                  child: Center(
                    child: Text(
                      getSubjectInitials(subjectName).toUpperCase(),
                      style: const TextStyle(
                        fontSize: 15,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
              ),
            ),

            /// **Text Section**
            Expanded(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      subjectName,
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: primaryColor,
                      ),
                      overflow: TextOverflow.visible,
                      maxLines: 2,
                    ),
                  ],
                ),
              ),
            ),

            /// **Image Section (SVG Image)**
            Padding(
                padding: const EdgeInsets.only(right: 16),
                child: Image.asset(
                  imagePath,
                  width: 50,
                  height: 50,
                )),
          ],
        ),
      ),
    );
  }
}

/// **Custom Clipper for Right Curved Design**
class RightCurveClipper extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    Path path = Path();
    path.moveTo(0, 0);
    path.lineTo(size.width * 0.75, 0);
    path.quadraticBezierTo(
      size.width,
      size.height / 2,
      size.width * 0.75,
      size.height,
    );
    path.lineTo(0, size.height);
    path.close();
    return path;
  }

  @override
  bool shouldReclip(CustomClipper<Path> oldClipper) => false;
}
