import 'package:flutter/material.dart';

class CustomSearchBar extends StatelessWidget {
  final TextEditingController controller;
  final VoidCallback onPressed;
  final Color? secondaryColor;
  final String? subject;

  const CustomSearchBar(
      {super.key,
      required this.controller,
      required this.onPressed,
      this.secondaryColor,
      this.subject});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Container(
        margin: const EdgeInsets.only(top: 50, bottom: 15),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(30),
          border: Border.all(
            color: secondaryColor ?? Colors.blue,
          ),
        ),
        child: Row(
          children: [
            Expanded(
              child: TextField(
                controller: controller,
                decoration: InputDecoration(
                  prefixIcon: Icon(
                    Icons.search,
                    color: secondaryColor ?? Colors.blue,
                  ),
                  hintText: "Search",
                  hintStyle: TextStyle(
                    color: secondaryColor ?? Colors.blue,
                  ),
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.symmetric(
                    vertical: 12,
                    horizontal: 15,
                  ),
                ),
                style: TextStyle(
                  color: secondaryColor ?? Colors.blue,
                ),
              ),
            ),
            Container(
              decoration: BoxDecoration(
                color: secondaryColor ?? Colors.blue,
                borderRadius: const BorderRadius.only(
                  topRight: Radius.circular(30),
                  bottomRight: Radius.circular(30),
                ),
              ),
              child: SizedBox(
                width: 110,
                child: TextButton(
                  onPressed: onPressed,
                  style: TextButton.styleFrom(
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                        horizontal: 10, vertical: 10),
                    minimumSize: Size.zero,
                  ),
                  child: Text(
                    subject != null ? subject! : "All Subject",
                    overflow: TextOverflow.visible,
                    maxLines: 2,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
