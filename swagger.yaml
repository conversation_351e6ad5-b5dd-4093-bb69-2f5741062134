openapi: 3.0.0
info:
  title: EddyOwl mobile app
  description: API for eddyowl flutter mobile app
  version: 1.0.0

servers:
- url: http://localhost/v1/api
  description: Local server
- url: https://api.example.com
  description: Production server

paths:
  /student/subjects/{studentId}:
    get:
      summary: Get the list of subjects for a student
      description: Retrieves a list of subjects available for a student based on their student ID.
      parameters:
      - name: studentId
        in: path
        required: true
        description: The ID of the student
        schema:
          type: string
      responses:
        "200":
          description: Successfully retrieved subjects
          content:
            application/json:
              schema:
                type: object
                properties:
                  subjects:
                    type: array
                    items:
                      $ref: "#/components/schemas/Subject"
              example:
                subjects:
                - subjectName: "Maths"
                - subjectName: "Science"
        "400":
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "403":
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "404":
          description: Not Found
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "500":
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "503":
          description: Service Unavailable
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

  /student/assignments:
    get:
      summary: Get assignments for a student
      description: Retrieves the list of assignments for a specific student based on their student ID.
      parameters:
      - name: studentId
        in: query
        required: true
        description: The ID of the student
        schema:
          type: string
      responses:
        "200":
          description: Successfully retrieved assignments
          content:
            application/json:
              schema:
                type: object
                properties:
                  assignments:
                    type: array
                    items:
                      $ref: "#/components/schemas/Assignment"
              example:
                assignments:
                - name: "Unit Test 1"
                  subjectName: "Maths"
                  topics:
                  - chapter: "Chapter 1"
                    topics: [ "Linear Equation", "Algebra", "Trigonometry" ]
                  status: "graded"
                  date: "20 Sept"
                  totalScore: 100
                  achievedScore: 95.5
        "400":
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "403":
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "404":
          description: Not Found
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "500":
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "503":
          description: Service Unavailable
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

  /student/assignments/{assignmentId}/view-assignment:
    get:
      summary: Get details of a specific assignment
      description: Retrieves the assignment details based on the assignment ID.
      parameters:
      - name: assignmentId
        in: path
        required: true
        description: The unique ID of the assignment
        schema:
          type: string
      responses:
        "200":
          description: Successfully retrieved assignment details
          content:
            application/json:
              schema:
                type: object
                properties:
                  studentResponses:
                    type: array
                    items:
                      $ref: "#/components/schemas/StudentResponse"
              example:
                studentResponses:
                - feedback: "The answer is incorrect. Cell Theory was proposed by Robert Hooke."
                  question: "Which scientist proposed the Cell Theory?"
                  questionNumber: 1
                  questionRubric: "(a) Robert Hooke"
                  questionScore: 1
                  achievedScore: 0
                  studentResponse: "(c) Matthias Schleiden"
                  topics:
                  - chapter: "Chapter 2"
                    topics: [ "Cell Theory" ]
        "400":
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "403":
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "404":
          description: Not Found
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "500":
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "503":
          description: Service Unavailable
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

components:
  schemas:
    ErrorResponse:
      type: string
      example: "Unable"

    SuccessResponse:
      type: object
      properties:
        id:
          type: string
        message:
          type: string

    Subject:
      type: object
      properties:
        subjectName:
          type: string

    Assignment:
      type: object
      properties:
        name:
          type: string
        subjectName:
          type: string
        topics:
          type: array
          items:
            $ref: "#/components/schemas/Topic"
        status:
          type: string
        date:
          type: string
        totalScore:
          type: integer
        achievedScore:
          type: number
          format: float

    Topic:
      type: object
      properties:
        chapter:
          type: string
        topics:
          type: array
          items:
            type: string

    StudentResponse:
      type: object
      properties:
        feedback:
          type: string
        question:
          type: string
        questionNumber:
          type: integer
        questionRubric:
          type: string
        questionScore:
          type: number
        achievedScore:
          type: number
        studentResponse:
          type: string
        topics:
          type: array
          items:
            $ref: "#/components/schemas/Topic"
