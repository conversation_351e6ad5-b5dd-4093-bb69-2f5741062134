import 'package:flutter/material.dart';
import 'package:auth0_flutter/auth0_flutter.dart';
import '../env.dart';

class AuthService extends ChangeNotifier {
  static final AuthService _instance = AuthService._internal();
  factory AuthService() => _instance;
  AuthService._internal();

  final auth0 = Auth0(Env.auth0Domain, Env.auth0ClientId);
  Credentials? _credentials;
  UserProfile? _userProfile;
  List<String> _userRoles = [];

  bool get isAuthenticated => _credentials != null;
  UserProfile? get userProfile => _userProfile;
  String? get accessToken => _credentials?.accessToken;
  List<String> get userRoles => _userRoles;

  // Email/Password Sign Up
  Future<void> signUpWithEmail({
    required String email,
    required String password,
    required String name,
  }) async {
    try {
      // Use database connection for email/password signup
      await auth0.api.signup(
        email: email,
        password: password,
        connection: 'Username-Password-Authentication',
        userMetadata: {'name': name},
      );
      
      // After signup, login automatically
      await loginWithEmail(email: email, password: password);
    } catch (e) {
      debugPrint('Signup error: $e');
      rethrow;
    }
  }

  // Email/Password Login
  Future<void> loginWithEmail({
    required String email,
    required String password,
  }) async {
    try {
      _credentials = await auth0.webAuthentication().login(
        parameters: {
          'username': email,
          'password': password,
          'connection': 'Username-Password-Authentication',
          'realm': 'Username-Password-Authentication',
        },
      );
      
      await _fetchUserProfile();
      notifyListeners();
    } catch (e) {
      debugPrint('Login error: $e');
      rethrow;
    }
  }

  // Google Sign In
  Future<void> loginWithGoogle() async {
    try {
      _credentials = await auth0.webAuthentication().login(
        parameters: {
          'connection': 'google-oauth2',
        },
      );
      
      await _fetchUserProfile();
      notifyListeners();
    } catch (e) {
      debugPrint('Google login error: $e');
      rethrow;
    }
  }

  // Fetch user profile helper
  Future<void> _fetchUserProfile() async {
    try {
      if (_credentials?.accessToken != null) {
        _userProfile = await auth0.api.userProfile(accessToken: _credentials!.accessToken);

        // Get user roles from app_metadata
        if (_userProfile != null) {
          final Map<String, dynamic>? metadata = _userProfile?.toMap()['app_metadata'];
          _userRoles = List<String>.from(metadata?['roles'] ?? []);
          debugPrint('User Roles: $_userRoles');
        }
      }
    } catch (e) {
      debugPrint('Error fetching user profile: $e');
      _userRoles = [];
    }
  }

  Future<void> logout() async {
    try {
      await auth0.webAuthentication().logout();
      _credentials = null;
      _userProfile = null;
      _userRoles = [];
      notifyListeners();
    } catch (e) {
      debugPrint('Logout error: $e');
      rethrow;
    }
  }
}

extension on UserProfile {
  Map<String, dynamic> toMap() {
    return {
      'app_metadata': {},  // Default empty metadata
      // Add other profile fields as needed
    };
  }
}





