import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:eddyowl_student_mobile/src/widgets/circular_score_indicator.dart';

class PageHeader extends StatelessWidget {
  final String title;
  final String subtitle;
  final int? achievedScore;
  final int totalScore;

  const PageHeader({
    super.key,
    required this.title,
    required this.subtitle,
    this.achievedScore,
    this.totalScore = 100,
  });

  @override
  Widget build(BuildContext context) {
    // Set system UI style (transparent status bar and light icons)
    SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent, // Transparent status bar
      statusBarIconBrightness:
          Brightness.light, // Light icons in the status bar
    ));

    return Stack(
      children: [
        /// Gradient Background with Curved Bottom
        ClipPath(
          clipper: BottomCurveClipper(),
          child: Container(
            width: double.infinity,
            padding:
                const EdgeInsets.only(top: 60, left: 20, right: 20, bottom: 50),
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Color(0xFF324EEA),
                  Color(0xFF1988E5),
                  Color(0xFF00C3E0),
                ],
                stops: [0.0, 0.10, 1.0],
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // Conditionally add score circle if achievedScore is provided
                if (achievedScore != null) ...[
                  CircularScoreIndicator(
                    achievedScore: achievedScore!,
                    totalScore: totalScore,
                  ),
                  const SizedBox(height: 5),
                ],
                Text(
                  title,
                  style: TextStyle(
                    fontSize: achievedScore != null ? 35 : 24,
                    fontWeight: achievedScore != null
                        ? FontWeight.w400
                        : FontWeight.bold,
                    color: Colors.white,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 5),
                Text(
                  subtitle,
                  style: TextStyle(
                    fontSize: achievedScore != null ? 18 : 16,
                    fontWeight: achievedScore != null
                        ? FontWeight.w400
                        : FontWeight.normal,
                    color:
                        achievedScore != null ? Colors.white : Colors.white70,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 2,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}

/// Custom Clipper for Curved Bottom (remains the same as in the original code)
class BottomCurveClipper extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    Path path = Path();
    path.lineTo(0, size.height - 40); // Move down more for a deeper curve
    path.quadraticBezierTo(
      size.width / 2, size.height + 40, // Move control point down
      size.width, size.height - 40, // Ending point
    );
    path.lineTo(size.width, 0);
    path.close();
    return path;
  }

  @override
  bool shouldReclip(CustomClipper<Path> oldClipper) => false;
}
